<script lang="ts">
	interface CaptionSegment {
		start: number;
		end: number;
		text: string;
	}

	let script = $state('Welcome to AI Digital Video. This is a test of our text-to-speech and caption generation system. You can customize the appearance of these captions in real-time.');
	let isGenerating = $state(false);
	let generationStep = $state('');
	let error = $state('');

	// Video data
	let audioUrl = $state('');
	let captions = $state<CaptionSegment[]>([]);
	let currentCaption = $state('');
	let audioDuration = $state(0);
	
	// Caption styling
	let captionStyle = $state({
		fontSize: 18,
		color: '#EE8C36',
		backgroundColor: 'rgba(0, 0, 0, 0.8)',
		fontFamily: 'Arial',
		position: 'bottom',
		padding: 12
	});
	
	// Voice options
	let selectedVoice = $state('alloy');
	let speechSpeed = $state(1.0);
	
	const voices = [
		{ value: 'alloy', label: 'Alloy (Neutral)' },
		{ value: 'echo', label: 'Echo (Male)' },
		{ value: 'fable', label: 'Fable (British Male)' },
		{ value: 'onyx', label: 'Onyx (Deep Male)' },
		{ value: 'nova', label: 'Nova (Female)' },
		{ value: 'shimmer', label: 'Shimmer (Soft Female)' }
	];
	
	async function generateVideo() {
		if (!script.trim()) {
			error = 'Please enter a script';
			return;
		}
		
		isGenerating = true;
		error = '';
		audioUrl = '';
		captions = [];
		currentCaption = '';
		
		try {
			generationStep = 'Generating speech audio...';
			
			const response = await fetch('/api/generate-video', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					script: script.trim(),
					voice: selectedVoice,
					speed: speechSpeed
				})
			});
			
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || 'Generation failed');
			}
			
			generationStep = 'Processing captions...';
			const result = await response.json();
			
			// Set the generated data
			audioUrl = result.audio.url;
			captions = result.captions.segments;
			audioDuration = result.audio.duration;
			
			generationStep = 'Complete!';
			console.log('Video generation completed:', result);
			
		} catch (err) {
			console.error('Generation error:', err);
			error = (err instanceof Error ? err.message : String(err)) || 'Failed to generate video';
		} finally {
			isGenerating = false;
			generationStep = '';
		}
	}

	function updateCaption(currentTime: number) {
		const active = captions.find(cap =>
			currentTime >= cap.start && currentTime <= cap.end
		);
		currentCaption = active?.text || '';
	}
	
	function resetGeneration() {
		audioUrl = '';
		captions = [];
		currentCaption = '';
		error = '';
		audioDuration = 0;
	}
</script>

<div class="container mx-auto max-w-4xl p-6">
	<h1 class="text-3xl font-bold text-[#EE8C36] mb-8">AI Video Generator</h1>
	
	<!-- Script Input -->
	<div class="mb-6">
		<label for="script" class="block text-sm font-medium mb-2">Video Script</label>
		<textarea
			id="script"
			bind:value={script}
			placeholder="Enter your script here... (max 4096 characters for 30s video)"
			class="w-full h-32 p-3 border rounded-lg resize-none focus:ring-2 focus:ring-[#EE8C36] focus:border-transparent"
			maxlength="4096"
		></textarea>
		<div class="text-sm text-gray-500 mt-1">
			{script.length}/4096 characters
		</div>
	</div>
	
	<!-- Voice & Speed Controls -->
	<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
		<div>
			<label for="voice" class="block text-sm font-medium mb-2">Voice</label>
			<select
				id="voice"
				bind:value={selectedVoice}
				class="w-full p-2 border rounded-lg focus:ring-2 focus:ring-[#EE8C36]"
			>
				{#each voices as voice}
					<option value={voice.value}>{voice.label}</option>
				{/each}
			</select>
		</div>
		
		<div>
			<label for="speed" class="block text-sm font-medium mb-2">
				Speech Speed: {speechSpeed}x
			</label>
			<input
				id="speed"
				type="range"
				bind:value={speechSpeed}
				min="0.25"
				max="4.0"
				step="0.25"
				class="w-full"
			/>
		</div>
	</div>
	
	<!-- Generate Button -->
	<div class="mb-8">
		<button
			onclick={generateVideo}
			disabled={isGenerating || !script.trim()}
			class="bg-[#EE8C36] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#d67a2e] disabled:opacity-50 disabled:cursor-not-allowed"
		>
			{isGenerating ? 'Generating...' : 'Generate Video'}
		</button>
		
		{#if audioUrl}
			<button
				onclick={resetGeneration}
				class="ml-4 bg-gray-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-600"
			>
				Reset
			</button>
		{/if}
	</div>
	
	<!-- Generation Status -->
	{#if isGenerating && generationStep}
		<div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
			<div class="flex items-center">
				<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
				<span class="text-blue-800">{generationStep}</span>
			</div>
		</div>
	{/if}
	
	<!-- Error Display -->
	{#if error}
		<div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
			<p class="text-red-800">{error}</p>
		</div>
	{/if}
	
	<!-- Video Player with Captions -->
	{#if audioUrl}
		<div class="mb-8">
			<h2 class="text-xl font-semibold mb-4">Generated Audio with Captions</h2>
			
			<div class="relative bg-black rounded-lg overflow-hidden">
				<!-- Audio Player -->
				<audio
					src={audioUrl}
					controls
					class="w-full"
					ontimeupdate={e => {
						const target = e.target as HTMLAudioElement;
						if (target) {
							updateCaption(target.currentTime);
						}
					}}
				>
					Your browser does not support the audio element.
				</audio>
				
				<!-- Caption Overlay -->
				{#if currentCaption}
					<div
						class="absolute bottom-4 left-4 right-4 text-center rounded px-3 py-2"
						style:font-size="{captionStyle.fontSize}px"
						style:color={captionStyle.color}
						style:background-color={captionStyle.backgroundColor}
						style:font-family={captionStyle.fontFamily}
						style:padding="{captionStyle.padding}px"
					>
						{currentCaption}
					</div>
				{/if}
			</div>
			
			<!-- Caption Info -->
			<div class="mt-4 text-sm text-gray-600">
				<p>Duration: {audioDuration.toFixed(1)}s | Segments: {captions.length}</p>
				{#if currentCaption}
					<p class="mt-1">Current: "{currentCaption}"</p>
				{/if}
			</div>
		</div>

		<!-- Caption Style Controls -->
		<div class="mt-8">
			<h3 class="text-lg font-semibold mb-4">Caption Styling</h3>

			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
				<!-- Font Size -->
				<div>
					<label for="font-size-slider" class="block text-sm font-medium mb-2">
						Font Size: {captionStyle.fontSize}px
					</label>
					<input
						id="font-size-slider"
						type="range"
						bind:value={captionStyle.fontSize}
						min="12"
						max="32"
						step="1"
						class="w-full"
					/>
				</div>

				<!-- Text Color -->
				<div>
					<label for="text-color-input" class="block text-sm font-medium mb-2">Text Color</label>
					<input
						id="text-color-input"
						type="color"
						bind:value={captionStyle.color}
						class="w-full h-10 rounded border"
					/>
				</div>

				<!-- Background Color -->
				<div>
					<label for="background-color-input" class="block text-sm font-medium mb-2">Background</label>
					<input
						id="background-color-input"
						type="color"
						bind:value={captionStyle.backgroundColor}
						class="w-full h-10 rounded border"
					/>
				</div>

				<!-- Font Family -->
				<div>
					<label for="font-family-select" class="block text-sm font-medium mb-2">Font Family</label>
					<select
						id="font-family-select"
						bind:value={captionStyle.fontFamily}
						class="w-full p-2 border rounded-lg focus:ring-2 focus:ring-[#EE8C36]"
					>
						<option value="Arial">Arial</option>
						<option value="Helvetica">Helvetica</option>
						<option value="Times New Roman">Times New Roman</option>
						<option value="Georgia">Georgia</option>
						<option value="Verdana">Verdana</option>
						<option value="Courier New">Courier New</option>
					</select>
				</div>

				<!-- Padding -->
				<div>
					<label for="padding-slider" class="block text-sm font-medium mb-2">
						Padding: {captionStyle.padding}px
					</label>
					<input
						id="padding-slider"
						type="range"
						bind:value={captionStyle.padding}
						min="4"
						max="24"
						step="2"
						class="w-full"
					/>
				</div>

				<!-- Reset Styles -->
				<div class="flex items-end">
					<button
						onclick={() => {
							captionStyle.fontSize = 18;
							captionStyle.color = '#EE8C36';
							captionStyle.backgroundColor = 'rgba(0, 0, 0, 0.8)';
							captionStyle.fontFamily = 'Arial';
							captionStyle.padding = 12;
						}}
						class="w-full bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600"
					>
						Reset Styles
					</button>
				</div>
			</div>
		</div>
	{/if}
</div>
