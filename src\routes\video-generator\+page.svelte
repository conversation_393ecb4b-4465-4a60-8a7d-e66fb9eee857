<script lang="ts">
	import { getContext } from 'svelte';

	interface CaptionSegment {
		start: number;
		end: number;
		text: string;
	}

	// Get dark mode context from layout
	const darkModeContext = getContext<{value: boolean, toggle: () => void}>('darkMode');
	const darkMode = $derived(darkModeContext?.value ?? false);

	let script = $state('Welcome to AI Digital Video. This is a test of our text-to-speech and caption generation system. You can customize the appearance of these captions in real-time.');
	let isGenerating = $state(false);
	let generationStep = $state('');
	let error = $state('');

	// Video data
	let audioUrl = $state('');
	let captions = $state<CaptionSegment[]>([]);
	let currentCaption = $state('');
	let audioDuration = $state(0);

	// Video canvas and rendering
	let videoCanvas: HTMLCanvasElement;
	let videoContext: CanvasRenderingContext2D;
	let isRecording = $state(false);
	let videoBlob = $state<Blob | null>(null);
	let videoUrl = $state('');
	
	// Caption styling
	let captionStyle = $state({
		fontSize: 18,
		color: '#EE8C36',
		backgroundColor: 'rgba(0, 0, 0, 0.8)',
		fontFamily: 'Arial',
		position: 'bottom',
		padding: 12
	});
	
	// Voice options
	let selectedVoice = $state('alloy');
	let speechSpeed = $state(1.0);
	
	const voices = [
		{ value: 'alloy', label: 'Alloy (Neutral)' },
		{ value: 'echo', label: 'Echo (Male)' },
		{ value: 'fable', label: 'Fable (British Male)' },
		{ value: 'onyx', label: 'Onyx (Deep Male)' },
		{ value: 'nova', label: 'Nova (Female)' },
		{ value: 'shimmer', label: 'Shimmer (Soft Female)' }
	];
	
	async function generateVideo() {
		if (!script.trim()) {
			error = 'Please enter a script';
			return;
		}
		
		isGenerating = true;
		error = '';
		audioUrl = '';
		captions = [];
		currentCaption = '';
		
		try {
			generationStep = 'Generating speech audio...';
			
			const response = await fetch('/api/generate-video', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					script: script.trim(),
					voice: selectedVoice,
					speed: speechSpeed
				})
			});
			
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || 'Generation failed');
			}
			
			generationStep = 'Processing captions...';
			const result = await response.json();
			
			// Set the generated data
			audioUrl = result.audio.url;
			captions = result.captions.segments;
			audioDuration = result.audio.duration;

			generationStep = 'Complete!';
			console.log('Video generation completed:', result);
			console.log('Audio URL length:', audioUrl?.length);
			console.log('Audio URL starts with:', audioUrl?.substring(0, 50));
			
		} catch (err) {
			console.error('Generation error:', err);
			error = (err instanceof Error ? err.message : String(err)) || 'Failed to generate video';
		} finally {
			isGenerating = false;
			generationStep = '';
		}
	}

	function updateCaption(currentTime: number) {
		const active = captions.find(cap =>
			currentTime >= cap.start && currentTime <= cap.end
		);
		currentCaption = active?.text || '';
	}

	// Initialize canvas when it's available
	$effect(() => {
		if (videoCanvas && !videoContext) {
			initVideoCanvas();
		}
	});

	// Update video canvas when caption changes
	$effect(() => {
		if (videoCanvas && videoContext) {
			drawVideoFrame(currentCaption);
		}
	});
	
	function resetGeneration() {
		audioUrl = '';
		captions = [];
		currentCaption = '';
		error = '';
		audioDuration = 0;
		videoUrl = '';
		videoBlob = null;
	}

	// Initialize video canvas
	function initVideoCanvas() {
		if (!videoCanvas) return;
		videoContext = videoCanvas.getContext('2d')!;

		// Set canvas size for 9:16 aspect ratio (YouTube Shorts)
		const width = 360;  // Base width
		const height = 640; // 9:16 ratio

		videoCanvas.width = width;
		videoCanvas.height = height;

		// Initial black frame
		drawVideoFrame('');
	}

	// Draw a single video frame with caption
	function drawVideoFrame(caption: string) {
		if (!videoContext) return;

		const width = videoCanvas.width;
		const height = videoCanvas.height;

		// Clear and fill with black background
		videoContext.fillStyle = '#000000';
		videoContext.fillRect(0, 0, width, height);

		// Draw caption at bottom if provided
		if (caption) {
			const captionY = height - 80; // Position from bottom
			const maxWidth = width - 40; // Padding from sides

			// Set caption styling
			videoContext.fillStyle = captionStyle.backgroundColor;
			videoContext.font = `${captionStyle.fontSize}px ${captionStyle.fontFamily}`;

			// Measure text for background
			const textMetrics = videoContext.measureText(caption);
			const textWidth = Math.min(textMetrics.width, maxWidth);
			const textHeight = captionStyle.fontSize + (captionStyle.padding * 2);

			// Draw caption background
			const bgX = (width - textWidth) / 2 - captionStyle.padding;
			const bgY = captionY - captionStyle.fontSize - captionStyle.padding;
			videoContext.fillRect(bgX, bgY, textWidth + (captionStyle.padding * 2), textHeight);

			// Draw caption text
			videoContext.fillStyle = captionStyle.color;
			videoContext.textAlign = 'center';
			videoContext.textBaseline = 'middle';
			videoContext.fillText(caption, width / 2, captionY - captionStyle.fontSize / 2, maxWidth);
		}
	}

	// Export video with audio and captions
	async function exportVideo() {
		if (!videoCanvas || !audioUrl || !captions.length) {
			console.error('Missing required data for video export');
			return;
		}

		isRecording = true;

		try {
			console.log('Starting video export...');

			// Create MediaRecorder for canvas
			const canvasStream = videoCanvas.captureStream(30); // 30 FPS

			// Create audio context and source
			const audioElement = new Audio(audioUrl);
			const audioContext = new AudioContext();
			const audioSource = audioContext.createMediaElementSource(audioElement);
			const audioDestination = audioContext.createMediaStreamDestination();
			audioSource.connect(audioDestination);
			audioSource.connect(audioContext.destination);

			// Combine video and audio streams
			const combinedStream = new MediaStream([
				...canvasStream.getVideoTracks(),
				...audioDestination.stream.getAudioTracks()
			]);

			// Set up MediaRecorder
			const mediaRecorder = new MediaRecorder(combinedStream, {
				mimeType: 'video/webm;codecs=vp9,opus'
			});

			const chunks: Blob[] = [];

			mediaRecorder.ondataavailable = (event) => {
				if (event.data.size > 0) {
					chunks.push(event.data);
				}
			};

			mediaRecorder.onstop = () => {
				videoBlob = new Blob(chunks, { type: 'video/webm' });
				videoUrl = URL.createObjectURL(videoBlob);
				isRecording = false;
				console.log('Video export completed!');
			};

			// Start recording
			mediaRecorder.start();

			// Play audio and animate captions
			audioElement.currentTime = 0;
			await audioElement.play();

			// Update captions during playback
			const updateInterval = setInterval(() => {
				if (audioElement.ended) {
					clearInterval(updateInterval);
					mediaRecorder.stop();
					canvasStream.getTracks().forEach(track => track.stop());
					audioContext.close();
					return;
				}

				updateCaption(audioElement.currentTime);
				drawVideoFrame(currentCaption);
			}, 1000 / 30); // 30 FPS updates

		} catch (error) {
			console.error('Video export failed:', error);
			isRecording = false;
		}
	}
</script>

<div class="container mx-auto max-w-4xl p-6">
	<h1 class="text-3xl font-bold text-[#EE8C36] mb-8">AI Video Generator</h1>
	
	<!-- Script Input -->
	<div class="mb-6">
		<label for="script" class="block text-sm font-medium mb-2">Video Script</label>
		<textarea
			id="script"
			bind:value={script}
			placeholder="Enter your script here... (max 4096 characters for 30s video)"
			class={"w-full h-32 p-3 border rounded-lg resize-none focus:ring-2 focus:ring-[#EE8C36] focus:border-transparent " + (darkMode ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900')}
			maxlength="4096"
		></textarea>
		<div class="text-sm text-gray-500 mt-1">
			{script.length}/4096 characters
		</div>
	</div>
	
	<!-- Voice & Speed Controls -->
	<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
		<div>
			<label for="voice" class="block text-sm font-medium mb-2">Voice</label>
			<select
				id="voice"
				bind:value={selectedVoice}
				class={"w-full p-2 border rounded-lg focus:ring-2 focus:ring-[#EE8C36] " + (darkMode ? 'bg-gray-800 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900')}
			>
				{#each voices as voice}
					<option value={voice.value}>{voice.label}</option>
				{/each}
			</select>
		</div>

		<div>
			<label for="speed" class="block text-sm font-medium mb-2">
				Speech Speed: {speechSpeed}x
			</label>
			<input
				id="speed"
				type="range"
				bind:value={speechSpeed}
				min="0.25"
				max="4.0"
				step="0.25"
				class={"w-full " + (darkMode ? 'accent-[#EE8C36]' : '')}
			/>
		</div>
	</div>
	
	<!-- Generate Button -->
	<div class="mb-8">
		<button
			onclick={generateVideo}
			disabled={isGenerating || !script.trim()}
			class="bg-[#EE8C36] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#d67a2e] disabled:opacity-50 disabled:cursor-not-allowed"
		>
			{isGenerating ? 'Generating...' : 'Generate Video'}
		</button>
		
		{#if audioUrl}
			<button
				onclick={resetGeneration}
				class="ml-4 bg-gray-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-600"
			>
				Reset
			</button>
		{/if}
	</div>
	
	<!-- Generation Status -->
	{#if isGenerating && generationStep}
		<div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
			<div class="flex items-center">
				<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
				<span class="text-blue-800">{generationStep}</span>
			</div>
		</div>
	{/if}
	
	<!-- Error Display -->
	{#if error}
		<div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
			<p class="text-red-800">{error}</p>
		</div>
	{/if}
	
	<!-- Video Generation Results -->
	{#if audioUrl}
		<div class="mb-8">
			<h2 class="text-xl font-semibold mb-4">Generated Video Preview</h2>

			<!-- Side-by-side layout: Controls left, Video right -->
			<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

				<!-- Left Column: Audio Controls & Info -->
				<div class="space-y-4">
					<!-- Audio Player -->
					<div>
						<h3 class="text-lg font-medium mb-2">Audio Controls</h3>
						<audio
							src={audioUrl}
							controls
							class="w-full"
							style="height: 54px; z-index: 10; position: relative;"
							ontimeupdate={e => {
								const target = e.target as HTMLAudioElement;
								if (target) {
									updateCaption(target.currentTime);
									drawVideoFrame(currentCaption);
								}
							}}
							onloadstart={() => console.log('Audio loading started')}
							oncanplay={() => console.log('Audio can play')}
							onerror={(e) => console.error('Audio error:', e)}
							onloadeddata={() => console.log('Audio data loaded')}
						>
							Your browser does not support the audio element.
						</audio>
					</div>

					<!-- Audio Info -->
					<div class="text-sm text-gray-600">
						<p>Duration: {audioDuration.toFixed(1)}s | Segments: {captions.length}</p>
						<p>Audio URL: {audioUrl ? 'Generated ✓' : 'Not available'} ({audioUrl ? audioUrl.substring(0, 50) + '...' : 'N/A'})</p>
						{#if currentCaption}
							<p class="mt-1">Current: "{currentCaption}"</p>
						{/if}
					</div>

					<!-- Action Buttons -->
					<div class="space-y-2">
						<button
							onclick={() => {
								if (audioUrl) {
									const audio = new Audio(audioUrl);
									audio.play().catch(e => console.error('Audio play failed:', e));
									console.log('Attempting to play audio directly...');
								}
							}}
							class="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
						>
							🔊 Test Audio Playback
						</button>

						<button
							onclick={exportVideo}
							disabled={isRecording || !audioUrl}
							class="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
						>
							{isRecording ? '🎬 Recording...' : '📹 Export Video'}
						</button>

						{#if videoUrl}
							<a
								href={videoUrl}
								download="ai-video.mp4"
								class="block w-full bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 text-center"
							>
								⬇️ Download Video
							</a>
						{/if}
					</div>
				</div>

				<!-- Right Column: Video Preview -->
				<div class="flex flex-col items-center">
					<h3 class="text-lg font-medium mb-2">Video Preview (9:16)</h3>
					<div class="relative">
						<canvas
							bind:this={videoCanvas}
							class="border border-gray-300 rounded-lg shadow-lg"
							style="max-width: 100%; height: auto;"
						></canvas>
						<div class="mt-2 text-sm text-gray-500 text-center">
							360x640 • YouTube Shorts Format
						</div>
					</div>
				</div>

			</div>
		</div>

		<!-- Caption Style Controls -->
		<div class="mt-8">
			<h3 class="text-lg font-semibold mb-4">Caption Styling</h3>

			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
				<!-- Font Size -->
				<div>
					<label for="font-size-slider" class="block text-sm font-medium mb-2">
						Font Size: {captionStyle.fontSize}px
					</label>
					<input
						id="font-size-slider"
						type="range"
						bind:value={captionStyle.fontSize}
						min="12"
						max="32"
						step="1"
						class={"w-full " + (darkMode ? 'accent-[#EE8C36]' : '')}
					/>
				</div>

				<!-- Text Color -->
				<div>
					<label for="text-color-input" class="block text-sm font-medium mb-2">Text Color</label>
					<input
						id="text-color-input"
						type="color"
						bind:value={captionStyle.color}
						class={"w-full h-10 rounded border " + (darkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-300')}
					/>
				</div>

				<!-- Background Color -->
				<div>
					<label for="background-color-input" class="block text-sm font-medium mb-2">Background</label>
					<input
						id="background-color-input"
						type="color"
						bind:value={captionStyle.backgroundColor}
						class={"w-full h-10 rounded border " + (darkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-300')}
					/>
				</div>

				<!-- Font Family -->
				<div>
					<label for="font-family-select" class="block text-sm font-medium mb-2">Font Family</label>
					<select
						id="font-family-select"
						bind:value={captionStyle.fontFamily}
						class={"w-full p-2 border rounded-lg focus:ring-2 focus:ring-[#EE8C36] " + (darkMode ? 'bg-gray-800 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900')}
					>
						<option value="Arial">Arial</option>
						<option value="Helvetica">Helvetica</option>
						<option value="Times New Roman">Times New Roman</option>
						<option value="Georgia">Georgia</option>
						<option value="Verdana">Verdana</option>
						<option value="Courier New">Courier New</option>
					</select>
				</div>

				<!-- Padding -->
				<div>
					<label for="padding-slider" class="block text-sm font-medium mb-2">
						Padding: {captionStyle.padding}px
					</label>
					<input
						id="padding-slider"
						type="range"
						bind:value={captionStyle.padding}
						min="4"
						max="24"
						step="2"
						class={"w-full " + (darkMode ? 'accent-[#EE8C36]' : '')}
					/>
				</div>

				<!-- Reset Styles -->
				<div class="flex items-end">
					<button
						onclick={() => {
							captionStyle.fontSize = 18;
							captionStyle.color = '#EE8C36';
							captionStyle.backgroundColor = 'rgba(0, 0, 0, 0.8)';
							captionStyle.fontFamily = 'Arial';
							captionStyle.padding = 12;
						}}
						class="w-full bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600"
					>
						Reset Styles
					</button>
				</div>
			</div>
		</div>
	{/if}
</div>
